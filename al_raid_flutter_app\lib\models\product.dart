import 'dart:convert';

class Product {
  final int id;
  final int userId;
  final String? image;
  final List<String>? images; // إضافة للصور المتعددة
  final String description;
  final String countryOfOrigin;
  final double price;
  final String type; // 'industrial' or 'food'
  final String status; // 'under_review', 'approved', 'rejected'
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? merchantName;
  // إضافة الخصائص المفقودة
  final String? name;
  final String? category;
  final int? stock;
  final Map<String, dynamic>? specifications;

  Product({
    required this.id,
    required this.userId,
    this.image,
    this.images,
    required this.description,
    required this.countryOfOrigin,
    required this.price,
    required this.type,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    this.merchantName,
    this.name,
    this.category,
    this.stock,
    this.specifications,
  });
  Product copyWith({
    int? id,
    int? userId,
    String? image,
    List<String>? images,
    String? description,
    String? countryOfOrigin,
    double? price,
    String? type,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? merchantName,
    String? name,
    String? category,
    int? stock,
    Map<String, dynamic>? specifications,
  }) {
    return Product(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      image: image ?? this.image,
      images: images ?? this.images,
      description: description ?? this.description,
      countryOfOrigin: countryOfOrigin ?? this.countryOfOrigin,
      price: price ?? this.price,
      type: type ?? this.type,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      merchantName: merchantName ?? this.merchantName,
      name: name ?? this.name,
      category: category ?? this.category,
      stock: stock ?? this.stock,
      specifications: specifications ?? this.specifications,
    );
  }

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id']?.toInt() ?? 0,
      userId: map['user_id']?.toInt() ?? 0,
      image: map['image'],
      images: map['images'] != null ? List<String>.from(map['images']) : null,
      description: map['description'] ?? '',
      countryOfOrigin: map['country_of_origin'] ?? '',
      price: (map['price'] ?? 0).toDouble(),
      type: map['type'] ?? '',
      status: map['status'] ?? '',
      createdAt: DateTime.tryParse(map['created_at'] ?? '') ?? DateTime.now(),
      updatedAt: map['updated_at'] != null
          ? DateTime.tryParse(map['updated_at'])
          : null,
      merchantName: map['merchant_name'],
      name: map['name'],
      category: map['category'],
      stock: map['stock']?.toInt(),
      specifications: map['specifications'] != null
          ? Map<String, dynamic>.from(map['specifications'])
          : null,
    );
  }

  factory Product.fromJson(String source) =>
      Product.fromMap(json.decode(source));
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'image': image,
      'images': images,
      'description': description,
      'country_of_origin': countryOfOrigin,
      'price': price,
      'type': type,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'merchant_name': merchantName,
      'name': name,
      'category': category,
      'stock': stock,
      'specifications': specifications,
    };
  }

  String toJson() => json.encode(toMap());

  @override
  String toString() {
    return 'Product(id: $id, userId: $userId, image: $image, description: $description, countryOfOrigin: $countryOfOrigin, price: $price, type: $type, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, merchantName: $merchantName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Product &&
        other.id == id &&
        other.userId == userId &&
        other.image == image &&
        other.description == description &&
        other.countryOfOrigin == countryOfOrigin &&
        other.price == price &&
        other.type == type &&
        other.status == status &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.merchantName == merchantName;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        image.hashCode ^
        description.hashCode ^
        countryOfOrigin.hashCode ^
        price.hashCode ^
        type.hashCode ^
        status.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        merchantName.hashCode;
  }

  bool get isIndustrial => type == 'industrial';
  bool get isFood => type == 'food';
  bool get isApproved => status == 'approved';
  bool get isUnderReview => status == 'under_review';
  bool get isRejected => status == 'rejected';

  String get statusDisplayName {
    switch (status) {
      case 'under_review':
        return 'Under Review';
      case 'approved':
        return 'Approved';
      case 'rejected':
        return 'Rejected';
      default:
        return status;
    }
  }

  String get typeDisplayName {
    switch (type) {
      case 'industrial':
        return 'Industrial';
      case 'food':
        return 'Food';
      default:
        return type;
    }
  }

  String get imageUrl {
    if (image == null || image!.isEmpty) return '';
    // استخدام الرابط المباشر للـ API المستضاف
    return 'https://alraid.ridcod.com/uploads/products/$image';
  }
}
